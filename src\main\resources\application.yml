server:
  port: 8081

spring:
  messages:
    basename: validation_messages
    encoding: UTF-8
    fallback-to-system-locale: false

management:
  opentelemetry:
    resource-attributes:
      service.name: my-spring-service
      service.namespace: common
      service.version: v1.0.0
      deployment.environment: dev

  endpoints:
    web:
      exposure:
        include: health, metrics

  tracing:
    enabled: true
    sampling:
      probability: 1.0 # 100% trace (dùng 0.1 cho 10%)

  otlp:
    tracing:
      endpoint: http://localhost:4317
      transport: grpc
    metrics:
      export:
        enabled: true
        url: http://localhost:4318/v1/metrics  # Collector endpoint
        step: 60s
    logging:
      endpoint: http://localhost:4317
      transport: grpc