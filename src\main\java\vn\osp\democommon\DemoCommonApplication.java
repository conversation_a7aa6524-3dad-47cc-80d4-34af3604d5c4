package vn.osp.democommon;

import org.slf4j.bridge.SLF4JBridgeHandler;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import java.util.logging.LogManager;

@SpringBootApplication(scanBasePackages = {
        "vn.osp.democommon",   // package chính
        "vn.osp.common"      // package common-lib
})
public class DemoCommonApplication {

    public static void main(String[] args) {

        //		// Remove default handlers attached to JUL
		LogManager.getLogManager().reset();
//		// Install SLF4J bridge
		SLF4JBridgeHandler.install();
        SpringApplication.run(DemoCommonApplication.class, args);
    }

}
