package vn.osp.democommon.controllers;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/users")
@Slf4j
public class OrderController {
    @GetMapping("info")
    public ResponseEntity<Boolean> info() {
//        return ResponseEntity.ok(true);
//        log.error("log.error");
//        log.warn("log.warn");
//        log.info("log.info");
        int est = 1 / 0;
        throw new NullPointerException();
//        throw new ConstraintViolationException("Validation failed", null);
    }

    @GetMapping("meo")
    public ResponseEntity<Boolean> hoang() {
        log.error("log.error");
        log.warn("log.warn");
        log.info("log.info");
        return ResponseEntity.ok(true);
//        throw new ConstraintViolationException("Validation failed", null);
    }

}
